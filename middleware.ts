import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from 'next-auth/middleware';

const debugRoutes = [
  '/debug-email',
  '/test-email',
  '/api/test/rakuten-token-public',
  '/api/public/user-status',
  '/api/public/reset-email-flag',
];

// Fonction middleware principale
async function mainMiddleware(req: NextRequest) {
  const { pathname } = req.nextUrl;

  // 1. Bloquer les routes de debug en priorité absolue
  if (debugRoutes.some(route => pathname.startsWith(route))) {
    return new NextResponse(null, { status: 404 });
  }

  // 2. Exécuter le middleware d'authentification pour toutes les autres routes
  // @ts-ignore
  return withAuth(
    async function middleware(req) {
      const { nextUrl } = req;
      const token = req.nextauth.token;
      const isLoggedIn = !!token;

      if (req.method === 'OPTIONS') {
        const response = new NextResponse(null, { status: 200 });
        response.headers.set('Access-Control-Allow-Origin', '*');
        response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
        return response;
      }

      const hasError = nextUrl.searchParams.has('error');
      const hasCallbackUrl = nextUrl.searchParams.has('callbackUrl');
      const isAuthRoute = nextUrl.pathname === '/login';
      const isLoginRedirectRoute = nextUrl.pathname === '/login-redirect';
      const isProtectedRoute = nextUrl.pathname.startsWith('/recommendations') ||
                              nextUrl.pathname.startsWith('/account') ||
                              nextUrl.pathname.startsWith('/collection') ||
                              nextUrl.pathname.startsWith('/statistics') ||
                              nextUrl.pathname.startsWith('/generating') ||
                              nextUrl.pathname.startsWith('/wishlist') ||
                              nextUrl.pathname.startsWith('/mes-envies') ||
                              nextUrl.pathname.startsWith('/social') ||
                              isLoginRedirectRoute;

      if (hasError) return NextResponse.next();
      if (isLoginRedirectRoute && isLoggedIn) return NextResponse.next();

      if (isAuthRoute && isLoggedIn && !hasCallbackUrl && !hasError) {
        return NextResponse.redirect(new URL('/login-redirect', nextUrl));
      }

      if (isProtectedRoute && !isLoggedIn) {
        return NextResponse.redirect(new URL('/login', nextUrl));
      }

      if (nextUrl.pathname === '/' && isLoggedIn) {
        return NextResponse.redirect(new URL('/login-redirect', nextUrl));
      }

      return NextResponse.next();
    },
    {
      callbacks: {
        authorized: ({ token, req }) => {
          const { pathname } = req.nextUrl;
          const isPublicRoute =
            pathname === '/' ||
            pathname.startsWith('/login') ||
            pathname.startsWith('/terms') ||
            pathname.startsWith('/privacy') ||
            pathname.startsWith('/api/auth') ||
            pathname.startsWith('/error') ||
            pathname.startsWith('/public/') ||
            pathname.startsWith('/u/') ||
            pathname === '/not-found';

          return isPublicRoute || !!token;
        },
      },
    }
  )(req);
}

export const middleware = mainMiddleware;

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - images/ (image files)
     */
    '/((?!_next/static|_next/image|favicon.ico|images|cover_art).*)',
  ],
};

